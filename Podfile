# Uncomment the next line to define a global platform for your project
platform :ios, '16.0'

target 'iOSProject' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for iOSProject
  
  # Azure Communication Services UI Library for calling functionality
  pod 'AzureCommunicationUICalling'
  pod 'SwipeActions'

  target 'iOSProjectTests' do
    inherit! :search_paths
    # Pods for testing
  end

  target 'iOSProjectUITests' do
    # Pods for testing
  end
end

# Post-install script to ensure proper configuration
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
      
      # Ensure proper Swift version
      if config.build_settings['SWIFT_VERSION'].nil?
        config.build_settings['SWIFT_VERSION'] = '5.0'
      end
      
      # Fix for Xcode 15 compatibility
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= ['$(inherited)']
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] << 'COCOAPODS=1'
    end
  end
end
